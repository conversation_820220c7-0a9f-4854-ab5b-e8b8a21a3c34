/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'League Spartan', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: hsl(0, 0%, 27%);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Header and Navigation */
.header {
  position: relative;
  z-index: 1000;
}

.nav {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
}

.nav-container {
  display: flex;
  align-items: center;
  padding: 2rem 1.5rem;
  position: relative;
}

.nav-toggle {
  background: none;
  border: none;
  cursor: pointer;
  z-index: 1002;
  display: block;
}

.nav-toggle .close-icon {
  display: none;
}

.nav-toggle.active .hamburger-icon {
  display: none;
}

.nav-toggle.active .close-icon {
  display: block;
}

.logo {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.nav-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: hsl(0, 100%, 100%);
  list-style: none;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.nav-menu.active {
  transform: translateX(0);
}

.nav-link {
  text-decoration: none;
  color: hsl(0, 0%, 0%);
  font-weight: 600;
  font-size: 1.2rem;
  text-transform: lowercase;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: hsl(0, 0%, 63%);
}

/* Hero Section */
.hero {
  position: relative;
}

.hero-slider {
  position: relative;
}

.slide {
  display: none;
}

.slide.active {
  display: block;
}

.slide-image img {
  width: 100%;
  height: 360px;
  object-fit: cover;
}

.slide-content {
  padding: 4rem 2rem;
}

.slide-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: hsl(0, 0%, 0%);
  margin-bottom: 1rem;
  line-height: 1.1;
}

.slide-content p {
  color: hsl(0, 0%, 63%);
  margin-bottom: 2rem;
  font-weight: 500;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  text-decoration: none;
  color: hsl(0, 0%, 0%);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5rem;
  transition: color 0.3s ease;
}

.cta-button:hover {
  color: hsl(0, 0%, 63%);
}

.arrow-icon {
  width: 40px;
  height: auto;
}

/* Slider Controls */
.slider-controls {
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
}

.slider-btn {
  background: hsl(0, 0%, 0%);
  border: none;
  padding: 1.5rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.slider-btn:hover {
  background: hsl(0, 0%, 27%);
}

.slider-btn img {
  width: 16px;
  height: auto;
}

/* About Section */
.about {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto auto auto;
}

.about-image-left img,
.about-image-right img {
  width: 100%;
  height: 240px;
  object-fit: cover;
}

.about-content {
  padding: 3rem 2rem;
}

.about-content h2 {
  font-size: 1rem;
  font-weight: 700;
  color: hsl(0, 0%, 0%);
  text-transform: uppercase;
  letter-spacing: 0.3rem;
  margin-bottom: 1rem;
}

.about-content p {
  color: hsl(0, 0%, 63%);
  font-weight: 500;
}

/* Attribution */
.attribution {
  font-size: 11px;
  text-align: center;
  padding: 1rem;
  background: hsl(0, 0%, 98%);
}

.attribution a {
  color: hsl(228, 45%, 44%);
}

/* Desktop Styles */
@media (min-width: 768px) {
  .nav-container {
    padding: 4rem 4rem 0;
  }
  
  .nav-toggle {
    display: none;
  }
  
  .logo {
    position: static;
    transform: none;
    margin-right: 3rem;
  }
  
  .nav-menu {
    position: static;
    background: none;
    flex-direction: row;
    justify-content: flex-start;
    gap: 2rem;
    transform: none;
    transition: none;
  }
  
  .nav-link {
    color: hsl(0, 100%, 100%);
    font-size: 1rem;
    position: relative;
  }
  
  .nav-link::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: hsl(0, 100%, 100%);
    transition: width 0.3s ease;
  }
  
  .nav-link:hover::after {
    width: 1.5rem;
  }
  
  .nav-link:hover {
    color: hsl(0, 100%, 100%);
  }
  
  .hero {
    display: grid;
    grid-template-columns: 3fr 2fr;
    min-height: 534px;
  }
  
  .slide-image {
    height: 534px;
  }
  
  .slide-image img {
    height: 100%;
  }
  
  .slide-content {
    padding: 7rem 6rem 4rem 4rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .slide-content h1 {
    font-size: 3rem;
  }
  
  .slider-controls {
    position: static;
    grid-column: 1;
    grid-row: 1;
    align-self: end;
    justify-self: end;
  }
  
  .about {
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-rows: auto;
  }
  
  .about-image-left img,
  .about-image-right img {
    height: 266px;
  }
  
  .about-content {
    padding: 4rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}
